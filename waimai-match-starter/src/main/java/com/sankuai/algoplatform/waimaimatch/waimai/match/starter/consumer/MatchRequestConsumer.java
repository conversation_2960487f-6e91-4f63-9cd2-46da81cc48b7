package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("listener")
public class MatchRequestConsumer {
    private static final Logger logger = LoggerFactory.getLogger(MatchRequestConsumer.class);

    @Autowired
    private ObjectMapper objectMapper;

    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(String msgBody) {
        logger.info(" 接收到消息 ：{}", msgBody);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 将 msgBody 反序列化为 PoiInfo 对象
     *
     * @param msgBody 消息体（支持 JSON 字符串、Map、JsonNode 等多种类型）
     * @return PoiInfo 对象，反序列化失败时返回 null
     */
    public PoiInfo deserializeMsgBodyToPoiInfo(Object msgBody) {
        if (msgBody == null) {
            logger.warn("msgBody 为空");
            return null;
        }

        try {
            // Jackson 可以智能处理多种类型的输入：JSON字符串、Map、JsonNode等
            return objectMapper.convertValue(msgBody, PoiInfo.class);
        } catch (IllegalArgumentException e) {
            logger.error("msgBody 转换为 PoiInfo 失败: {}", msgBody, e);
            return null;
        }
    }
}
