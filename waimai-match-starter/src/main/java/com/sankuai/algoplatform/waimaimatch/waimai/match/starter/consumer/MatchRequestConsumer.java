package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("listener")
public class MatchRequestConsumer {
    private static final Logger logger = LoggerFactory.getLogger(MatchRequestConsumer.class);

    @Autowired
    private ObjectMapper objectMapper;

    // msgBody类型要与消息实际类型一致，如果发送方以Long型发送消息，此处应改为Long msgBody
    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(Long msgBody) {
        logger.info(" 接收到消息 ：{}", msgBody);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 使用 Jackson 将 JSON 字符串反序列化为 PoiInfo 对象
     *
     * @param jsonString JSON 字符串
     * @return PoiInfo 对象，反序列化失败时返回 null
     */
    public PoiInfo deserializePoiInfo(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            logger.warn("JSON 字符串为空，无法反序列化");
            return null;
        }

        try {
            return objectMapper.readValue(jsonString, PoiInfo.class);
        } catch (JsonProcessingException e) {
            logger.error("JSON 反序列化失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 将 msgBody 反序列化为 PoiInfo 对象（简化版本）
     *
     * @param msgBody 消息体
     * @return PoiInfo 对象，反序列化失败时返回 null
     */
    public PoiInfo deserializeMsgBodyToPoiInfo(Object msgBody) {
        if (msgBody == null) {
            logger.warn("msgBody 为空");
            return null;
        }

        try {
            // Jackson 可以直接处理多种类型的输入
            return objectMapper.convertValue(msgBody, PoiInfo.class);
        } catch (IllegalArgumentException e) {
            logger.error("msgBody 转换为 PoiInfo 失败: {}", msgBody, e);
            return null;
        }
    }

    /**
     * 通用的反序列化方法，支持任意类型
     *
     * @param source 源数据（可以是 JSON 字符串、Map、其他对象等）
     * @param targetClass 目标类型
     * @param <T> 泛型类型
     * @return 反序列化后的对象，失败时返回 null
     */
    public <T> T deserialize(Object source, Class<T> targetClass) {
        if (source == null) {
            logger.warn("源数据为空，无法反序列化为 {}", targetClass.getSimpleName());
            return null;
        }

        try {
            if (source instanceof String) {
                return objectMapper.readValue((String) source, targetClass);
            } else {
                return objectMapper.convertValue(source, targetClass);
            }
        } catch (Exception e) {
            logger.error("反序列化为 {} 失败，源数据: {}", targetClass.getSimpleName(), source, e);
            return null;
        }
    }
}
