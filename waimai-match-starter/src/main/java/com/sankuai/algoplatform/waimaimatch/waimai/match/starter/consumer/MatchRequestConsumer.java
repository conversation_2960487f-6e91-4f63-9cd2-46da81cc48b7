package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("listener")
public class MatchRequestConsumer {
    private static final Logger logger = LoggerFactory.getLogger(MatchRequestConsumer.class);

    @Autowired
    private ObjectMapper objectMapper;

    // msgBody类型要与消息实际类型一致，如果发送方以Long型发送消息，此处应改为Long msgBody
    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(Long msgBody) {
        logger.info(" 接收到消息 ：{}", msgBody);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 使用 Jackson 将 JSON 字符串反序列化为 PoiInfo 对象
     *
     * @param jsonString JSON 字符串
     * @return PoiInfo 对象，反序列化失败时返回 null
     */
    public PoiInfo deserializePoiInfo(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            logger.warn("JSON 字符串为空，无法反序列化");
            return null;
        }

        try {
            return objectMapper.readValue(jsonString, PoiInfo.class);
        } catch (JsonProcessingException e) {
            logger.error("JSON 反序列化失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 将 msgBody 反序列化为 PoiInfo 对象
     *
     * @param msgBody 消息体
     * @return PoiInfo 对象，反序列化失败时返回 null
     */
    public PoiInfo deserializeMsgBodyToPoiInfo(Object msgBody) {
        if (msgBody == null) {
            logger.warn("msgBody 为空");
            return null;
        }

        try {
            // Jackson 可以直接处理多种类型的输入
            return objectMapper.convertValue(msgBody, PoiInfo.class);
        } catch (IllegalArgumentException e) {
            logger.error("msgBody 转换为 PoiInfo 失败: {}", msgBody, e);
            return null;
        }
    }
}
