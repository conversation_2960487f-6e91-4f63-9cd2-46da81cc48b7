package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service("listener")
public class MatchRequestConsumer {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private ObjectMapper objectMapper = new ObjectMapper();

    // msgBody类型要与消息实际类型一致，如果发送方以Long型发送消息，此处应改为Long msgBody
    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(Long msgBody) {
        logger.info(" 接收到消息 ：{}", msgBody);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 将 JSON 字符串反序列化为 PoiInfo 对象
     *
     * @param jsonString JSON 字符串
     * @return PoiInfo 对象，如果反序列化失败则返回 null
     */
    public PoiInfo deserializePoiInfo(String jsonString) {
        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                logger.warn("输入的 JSON 字符串为空");
                return null;
            }

            PoiInfo poiInfo = objectMapper.readValue(jsonString, PoiInfo.class);
            logger.info("成功反序列化 PoiInfo 对象: tempPoiId={}, eid={}, poiName={}",
                       poiInfo.getTempPoiId(), poiInfo.getEid(), poiInfo.getPoiName());
            return poiInfo;

        } catch (Exception e) {
            logger.error("反序列化 PoiInfo 失败，JSON: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 将 msgBody 反序列化为 PoiInfo 对象
     *
     * @param msgBody 消息体，可以是 String 或其他类型
     * @return PoiInfo 对象，如果反序列化失败则返回 null
     */
    public PoiInfo deserializeMsgBodyToPoiInfo(Object msgBody) {
        try {
            if (msgBody == null) {
                logger.warn("msgBody 为空");
                return null;
            }

            String jsonString;
            if (msgBody instanceof String) {
                jsonString = (String) msgBody;
            } else {
                // 如果 msgBody 不是字符串，先将其转换为 JSON 字符串
                jsonString = objectMapper.writeValueAsString(msgBody);
            }

            return deserializePoiInfo(jsonString);

        } catch (Exception e) {
            logger.error("处理 msgBody 失败: {}", msgBody, e);
            return null;
        }
    }
}
