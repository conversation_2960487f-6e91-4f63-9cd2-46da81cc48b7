package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import scala.util.parsing.json.JSONObject;

@Service("listener")
public class MatchRequestConsumer {
    private Logger logger = LoggerFactory.getLogger(getClass());

    // msgBody类型要与消息实际类型一致，如果发送方以Long型发送消息，此处应改为Long msgBody
    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(Long msgBody) {
        logger.info(" 接收到消息 ：{}", msgBody);
        
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
